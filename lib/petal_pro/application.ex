defmodule PetalPro.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      PetalProWeb.Telemetry,
      PetalPro.Repo,
      {DNSCluster, query: Application.get_env(:petal_pro, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: PetalPro.PubSub},
      PetalProWeb.Presence,
      {Task.Supervisor, name: PetalPro.BackgroundTask},

      # Start the Finch HTTP client for sending emails and Tesla
      {Finch, name: PetalPro.Finch},
      {Oban, Application.fetch_env!(:petal_pro, Oban)},

      # Start the meter collection GenServer
      # Conditionally start based on environment:
      # - Always start in test environment
      # - Only start in dev/prod if explicitly enabled via config
      maybe_start_meter_collection_genserver(),

      # Start a worker by calling: PetalPro.Worker.start_link(arg)
      # {PetalPro.Worker, arg}

      # Start to serve requests, typically the last entry
      PetalProWeb.Endpoint
    ]
    |> Enum.reject(&is_nil/1)

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: PetalPro.Supervisor]
    Supervisor.start_link(children, opts)
  end

  defp maybe_start_meter_collection_genserver do
    case Application.get_env(:petal_pro, :env) do
      :test ->
        # Always start in test environment to ensure tests pass
        PetalPro.Billing.Meters.CollectMeterEvents

      _ ->
        # In dev/prod, only start if explicitly enabled via config
        if Application.get_env(:petal_pro, :enable_meter_collection, false) do
          PetalPro.Billing.Meters.CollectMeterEvents
        else
          # Return nil to be filtered out by Enum.reject(&is_nil/1)
          nil
        end
    end
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    PetalProWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
